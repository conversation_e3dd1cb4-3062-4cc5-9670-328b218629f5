using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using DriverManagementSystem.Data;
using DriverManagementSystem.Models;

namespace DriverManagementSystem.Services
{
    /// <summary>
    /// خدمة ربط بيانات السائقين مع العروض والتقارير
    /// </summary>
    public class DriverDataService
    {
        private readonly ApplicationDbContext _context;

        public DriverDataService(ApplicationDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// جلب عروض الأسعار مع ربطها بجدول السائقين لضمان الحصول على رقم التلفون الصحيح
        /// </summary>
        public async Task<List<PriceOfferItem>> GetEnhancedPriceOffersAsync(string visitNumber)
        {
            try
            {
                var priceOffers = new List<PriceOfferItem>();

                // جلب العروض من جدول DriverQuotes
                var driverQuotes = await _context.DriverQuotes
                    .Where(dq => dq.Notes.Contains(visitNumber))
                    .ToListAsync();

                // جلب جميع السائقين للمطابقة
                var allDrivers = await _context.Drivers.ToListAsync();

                for (int i = 0; i < driverQuotes.Count(); i++)
                {
                    var quote = driverQuotes[i];
                    
                    // البحث عن السائق في الجدول الرئيسي بعدة طرق
                    var driver = FindDriverByMultipleCriteria(allDrivers, quote);

                    // تحديد حالة الفوز بناءً على الملاحظات والحالة
                    bool isWinner = (quote.Status == QuoteStatus.Accepted) ||
                                   (quote.Notes != null && quote.Notes.Contains("-فائز"));

                    var priceOffer = new PriceOfferItem
                    {
                        SerialNumber = i + 1,
                        DriverName = quote.DriverName,
                        PhoneNumber = GetDriverPhoneNumber(driver, quote),
                        OfferedPrice = GetDriverPrice(driver, quote),
                        Status = isWinner ? "🏆 فائز" : GetOfferStatus(quote.Status),
                        IsWinner = isWinner
                    };

                    priceOffers.Add(priceOffer);
                }

                return priceOffers;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في جلب العروض المحسنة: {ex.Message}");
                return new List<PriceOfferItem>();
            }
        }

        /// <summary>
        /// البحث عن السائق بعدة معايير
        /// </summary>
        private Driver FindDriverByMultipleCriteria(List<Driver> allDrivers, DriverQuote quote)
        {
            // البحث بالاسم الكامل
            var driver = allDrivers.FirstOrDefault(d => 
                string.Equals(d.Name.Trim(), quote.DriverName.Trim(), StringComparison.OrdinalIgnoreCase));

            // البحث بكود السائق
            if (driver == null && !string.IsNullOrEmpty(quote.DriverCode))
            {
                driver = allDrivers.FirstOrDefault(d => d.DriverCode == quote.DriverCode);
            }

            // البحث بالمعرف
            if (driver == null && quote.DriverId > 0)
            {
                driver = allDrivers.FirstOrDefault(d => d.Id == quote.DriverId);
            }

            // البحث بالاسم الجزئي (في حالة وجود اختلافات طفيفة)
            if (driver == null)
            {
                driver = allDrivers.FirstOrDefault(d => 
                    d.Name.Contains(quote.DriverName) || quote.DriverName.Contains(d.Name));
            }

            return driver;
        }

        /// <summary>
        /// الحصول على رقم التلفون الصحيح
        /// </summary>
        private string GetDriverPhoneNumber(Driver driver, DriverQuote quote)
        {
            // أولوية للرقم من جدول السائقين الرئيسي
            if (driver != null && !string.IsNullOrWhiteSpace(driver.PhoneNumber))
            {
                return driver.PhoneNumber;
            }

            // إذا لم يوجد في الجدول الرئيسي، استخدم المحفوظ في العرض
            if (!string.IsNullOrWhiteSpace(quote.PhoneNumber))
            {
                return quote.PhoneNumber;
            }

            return "غير محدد";
        }

        /// <summary>
        /// الحصول على السعر الصحيح للسائق
        /// </summary>
        private decimal GetDriverPrice(Driver driver, DriverQuote quote)
        {
            // أولوية للسعر المحفوظ في العرض
            if (quote.QuotedPrice > 0)
            {
                return quote.QuotedPrice;
            }

            // إذا لم يوجد سعر في العرض، تقدير السعر بناءً على نوع المركبة
            if (driver != null && !string.IsNullOrWhiteSpace(driver.VehicleType))
            {
                var estimatedPrice = EstimatePriceByVehicleType(driver.VehicleType);
                if (estimatedPrice > 0)
                {
                    System.Diagnostics.Debug.WriteLine($"💰 تقدير سعر {driver.Name}: {estimatedPrice:N0} (بناءً على {driver.VehicleType})");
                    return estimatedPrice;
                }
            }

            // سعر افتراضي
            return 45000;
        }

        /// <summary>
        /// تقدير السعر بناءً على نوع المركبة
        /// </summary>
        private decimal EstimatePriceByVehicleType(string vehicleType)
        {
            if (string.IsNullOrWhiteSpace(vehicleType))
                return 0;

            var type = vehicleType.ToLower();

            // أسعار تقديرية بناءً على نوع المركبة
            if (type.Contains("هايلكس") || type.Contains("hilux"))
                return 50000;
            else if (type.Contains("باترول") || type.Contains("patrol"))
                return 55000;
            else if (type.Contains("رينجر") || type.Contains("ranger"))
                return 48000;
            else if (type.Contains("كولورادو") || type.Contains("colorado"))
                return 52000;
            else if (type.Contains("ديماكس") || type.Contains("dmax"))
                return 47000;
            else if (type.Contains("نافارا") || type.Contains("navara"))
                return 49000;
            else if (type.Contains("فورد") || type.Contains("ford"))
                return 48000;
            else if (type.Contains("شيفروليه") || type.Contains("chevrolet"))
                return 51000;
            else if (type.Contains("إيسوزو") || type.Contains("isuzu"))
                return 46000;
            else if (type.Contains("نيسان") || type.Contains("nissan"))
                return 49000;
            else if (type.Contains("تويوتا") || type.Contains("toyota"))
                return 50000;
            else
                return 45000; // سعر افتراضي
        }

        /// <summary>
        /// تحويل حالة العرض إلى نص عربي
        /// </summary>
        private string GetOfferStatus(QuoteStatus status)
        {
            return status switch
            {
                QuoteStatus.Pending => "قيد المراجعة",
                QuoteStatus.Accepted => "مقبول",
                QuoteStatus.Rejected => "مرفوض",
                _ when (int)status == 3 => "ملغي",
                _ => "غير محدد"
            };
        }

        /// <summary>
        /// إضافة أو تحديث رقم التلفون للسائق في جدول العروض
        /// </summary>
        public async Task<bool> UpdateDriverPhoneInQuotesAsync(string driverName, string phoneNumber)
        {
            try
            {
                var quotes = await _context.DriverQuotes
                    .Where(dq => dq.DriverName == driverName)
                    .ToListAsync();

                foreach (var quote in quotes)
                {
                    if (string.IsNullOrWhiteSpace(quote.PhoneNumber))
                    {
                        quote.PhoneNumber = phoneNumber;
                    }
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحديث رقم التلفون: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// مزامنة بيانات السائقين بين الجداول
        /// </summary>
        public async Task<bool> SynchronizeDriverDataAsync()
        {
            try
            {
                var drivers = await _context.Drivers.ToListAsync();
                var quotes = await _context.DriverQuotes.ToListAsync();

                foreach (var quote in quotes)
                {
                    var driver = drivers.FirstOrDefault(d => 
                        d.Name == quote.DriverName || d.DriverCode == quote.DriverCode);

                    if (driver != null)
                    {
                        // تحديث البيانات في جدول العروض
                        if (string.IsNullOrWhiteSpace(quote.PhoneNumber) && !string.IsNullOrWhiteSpace(driver.PhoneNumber))
                        {
                            quote.PhoneNumber = driver.PhoneNumber;
                        }

                        if (string.IsNullOrWhiteSpace(quote.VehicleType) && !string.IsNullOrWhiteSpace(driver.VehicleType))
                        {
                            quote.VehicleType = driver.VehicleType;
                        }

                        if (string.IsNullOrWhiteSpace(quote.VehicleNumber) && !string.IsNullOrWhiteSpace(driver.VehicleNumber))
                        {
                            quote.VehicleNumber = driver.VehicleNumber;
                        }
                    }
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في مزامنة البيانات: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// جلب إحصائيات السائقين والعروض
        /// </summary>
        public async Task<DriverStatistics> GetDriverStatisticsAsync()
        {
            try
            {
                var totalDrivers = await _context.Drivers.CountAsync();
                var totalQuotes = await _context.DriverQuotes.CountAsync();
                var acceptedQuotes = await _context.DriverQuotes.CountAsync(dq => dq.Status == QuoteStatus.Accepted);
                var driversWithPhones = await _context.Drivers.CountAsync(d => !string.IsNullOrWhiteSpace(d.PhoneNumber));

                return new DriverStatistics
                {
                    TotalDrivers = totalDrivers,
                    TotalQuotes = totalQuotes,
                    AcceptedQuotes = acceptedQuotes,
                    DriversWithPhones = driversWithPhones,
                    PhoneCompletionRate = totalDrivers > 0 ? (double)driversWithPhones / totalDrivers * 100 : 0
                };
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في جلب الإحصائيات: {ex.Message}");
                return new DriverStatistics();
            }
        }
    }

    /// <summary>
    /// إحصائيات السائقين
    /// </summary>
    public class DriverStatistics
    {
        public int TotalDrivers { get; set; }
        public int TotalQuotes { get; set; }
        public int AcceptedQuotes { get; set; }
        public int DriversWithPhones { get; set; }
        public double PhoneCompletionRate { get; set; }
    }
}
